import React, { useState, useRef, useEffect } from 'react';
import { Input, Typography } from 'antd';

const { Text } = Typography;

const EditableText = ({ 
  value = '', 
  onChange, 
  style = {},
  textStyle = {},
  placeholder = '点击编辑文本',
  maxLength = 200,
  disabled = false
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const inputRef = useRef(null);

  useEffect(() => {
    setEditValue(value);
  }, [value]);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleClick = () => {
    if (!disabled) {
      setIsEditing(true);
    }
  };

  const handleSave = () => {
    setIsEditing(false);
    if (onChange && editValue !== value) {
      onChange(editValue);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(value);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleBlur = () => {
    handleSave();
  };

  const handleChange = (e) => {
    setEditValue(e.target.value);
  };

  if (isEditing) {
    return (
      <Input
        ref={inputRef}
        value={editValue}
        onChange={handleChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        maxLength={maxLength}
        style={{
          border: '1px solid #1890ff',
          borderRadius: '2px',
          padding: '2px 4px',
          fontSize: 'inherit',
          fontFamily: 'inherit',
          fontWeight: 'inherit',
          color: 'inherit',
          background: '#fff',
          width: '100%',
          height: '100%',
          ...style
        }}
      />
    );
  }

  return (
    <div
      onClick={handleClick}
      style={{
        cursor: disabled ? 'default' : 'pointer',
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'inherit',
        padding: '2px 4px',
        borderRadius: '2px',
        transition: 'all 0.2s ease',
        ...style,
        ...(disabled ? {} : {
          ':hover': {
            backgroundColor: 'rgba(24, 144, 255, 0.1)',
            border: '1px dashed #1890ff'
          }
        })
      }}
      title={disabled ? '' : '点击编辑文本'}
    >
      <Text
        style={{
          fontSize: 'inherit',
          fontFamily: 'inherit',
          fontWeight: 'inherit',
          color: 'inherit',
          lineHeight: '1.2',
          wordBreak: 'break-all',
          width: '100%',
          textAlign: 'inherit',
          ...textStyle
        }}
      >
        {value || placeholder}
      </Text>
    </div>
  );
};

export default EditableText;
